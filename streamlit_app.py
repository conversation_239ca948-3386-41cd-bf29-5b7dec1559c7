#!/usr/bin/env python3
"""
Azure OpenAI Memory System - Streamlit Web Application
A secure, scalable chatbot with vector database storage and user authentication.
"""

import os
import warnings
# Suppress PyTorch warnings that don't affect functionality
warnings.filterwarnings("ignore", category=UserWarning, module="torch")
warnings.filterwarnings("ignore", message=".*torch.classes.*")

import streamlit as st
from datetime import datetime
import uuid
import bcrypt
import time
from typing import List, Dict, Optional

# Import our custom modules
from memory_manager import VectorMemoryManager
from auth_manager import AuthManager
from azure_client import AzureOpenAIClient

# Page configuration
st.set_page_config(
    page_title="Azure OpenAI Memory Chat",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better UI
st.markdown("""
<style>
    .main-header {
        text-align: center;
        color: #2E86AB;
        margin-bottom: 2rem;
    }
    .chat-message {
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .user-message {
        background-color: #E3F2FD;
        border-left: 4px solid #2196F3;
    }
    .assistant-message {
        background-color: #F3E5F5;
        border-left: 4px solid #9C27B0;
    }
    .typing-indicator {
        color: #666;
        font-style: italic;
    }
    .sidebar-section {
        margin: 1rem 0;
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #F8F9FA;
    }
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """Initialize Streamlit session state variables"""
    if 'authenticated' not in st.session_state:
        st.session_state.authenticated = False
    if 'username' not in st.session_state:
        st.session_state.username = None
    if 'user_id' not in st.session_state:
        st.session_state.user_id = None
    if 'messages' not in st.session_state:
        st.session_state.messages = []
    if 'memory_manager' not in st.session_state:
        st.session_state.memory_manager = None
    if 'azure_client' not in st.session_state:
        st.session_state.azure_client = None

def login_page():
    """Display login/registration page"""
    st.markdown("<h1 class='main-header'>🤖 Azure OpenAI Memory Chat</h1>", unsafe_allow_html=True)
    
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        st.markdown("### Welcome! Please login or register to continue.")
        
        tab1, tab2 = st.tabs(["Login", "Register"])
        
        with tab1:
            with st.form("login_form"):
                username = st.text_input("Username")
                password = st.text_input("Password", type="password")
                login_button = st.form_submit_button("Login")
                
                if login_button:
                    if username and password:
                        auth_manager = AuthManager()
                        user_id = auth_manager.authenticate_user(username, password)
                        if user_id:
                            st.session_state.authenticated = True
                            st.session_state.username = username
                            st.session_state.user_id = user_id
                            st.success("Login successful!")
                            st.rerun()
                        else:
                            st.error("Invalid username or password")
                    else:
                        st.error("Please enter both username and password")
        
        with tab2:
            with st.form("register_form"):
                new_username = st.text_input("Choose Username")
                new_password = st.text_input("Choose Password", type="password")
                confirm_password = st.text_input("Confirm Password", type="password")
                register_button = st.form_submit_button("Register")
                
                if register_button:
                    if new_username and new_password and confirm_password:
                        if new_password == confirm_password:
                            auth_manager = AuthManager()
                            if auth_manager.register_user(new_username, new_password):
                                st.success("Registration successful! Please login.")
                            else:
                                st.error("Username already exists")
                        else:
                            st.error("Passwords do not match")
                    else:
                        st.error("Please fill in all fields")

def sidebar():
    """Display sidebar with user info and controls"""
    with st.sidebar:
        st.markdown(f"<div class='sidebar-section'>", unsafe_allow_html=True)
        st.markdown(f"**👤 User:** {st.session_state.username}")
        st.markdown(f"**🆔 ID:** {st.session_state.user_id[:8]}...")
        st.markdown("</div>", unsafe_allow_html=True)
        
        st.markdown("### 🛠️ Controls")
        
        if st.button("🗑️ Clear Chat History"):
            if st.session_state.memory_manager:
                st.session_state.memory_manager.clear_user_history(st.session_state.user_id)
            st.session_state.messages = []
            st.success("Chat history cleared!")
            st.rerun()
        
        if st.button("🚪 Logout"):
            # Clear session state
            for key in list(st.session_state.keys()):
                del st.session_state[key]
            st.rerun()
        
        st.markdown("### 📊 Memory Stats")
        if st.session_state.memory_manager:
            stats = st.session_state.memory_manager.get_user_stats(st.session_state.user_id)
            st.metric("Total Conversations", stats.get('total_conversations', 0))
            st.metric("Memory Entries", stats.get('memory_entries', 0))
        
        st.markdown("### ℹ️ About")
        st.info("""
        This chatbot uses Azure OpenAI with vector database storage for persistent memory.
        
        **Features:**
        - Conversation memory
        - Semantic search
        - User isolation
        - Secure storage
        """)

def display_chat_messages():
    """Display chat messages"""
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])

def main_chat_interface():
    """Main chat interface"""
    st.markdown("<h1 class='main-header'>🤖 Azure OpenAI Memory Chat</h1>", unsafe_allow_html=True)
    
    # Initialize managers if not already done
    if st.session_state.memory_manager is None:
        st.session_state.memory_manager = VectorMemoryManager()
    
    if st.session_state.azure_client is None:
        st.session_state.azure_client = AzureOpenAIClient()
    
    # Display existing messages
    display_chat_messages()
    
    # Chat input
    if prompt := st.chat_input("Type your message here..."):
        # Add user message to chat
        st.session_state.messages.append({"role": "user", "content": prompt})
        with st.chat_message("user"):
            st.markdown(prompt)
        
        # Generate assistant response
        with st.chat_message("assistant"):
            with st.spinner("Thinking..."):
                # Get relevant context from memory
                context = st.session_state.memory_manager.get_relevant_context(
                    st.session_state.user_id, prompt
                )
                
                # Generate response using Azure OpenAI
                response = st.session_state.azure_client.generate_response(
                    prompt, context, st.session_state.user_id
                )
                
                st.markdown(response)
        
        # Add assistant response to chat
        st.session_state.messages.append({"role": "assistant", "content": response})
        
        # Store conversation in vector database
        st.session_state.memory_manager.store_conversation(
            st.session_state.user_id,
            prompt,
            response
        )

def main():
    """Main application function"""
    initialize_session_state()
    
    if not st.session_state.authenticated:
        login_page()
    else:
        sidebar()
        main_chat_interface()

if __name__ == "__main__":
    main()
