# Deployment Guide - Azure OpenAI Memory Chat

This guide covers deploying the Azure OpenAI Memory Chat application in different environments.

## 🚀 Local Development

### Quick Start
```bash
# 1. Install dependencies
python install.py

# 2. Configure environment
# Edit .env file with your Azure OpenAI credentials

# 3. Run the application
streamlit run streamlit_app.py
```

### Manual Setup
```bash
# Install requirements
pip install -r requirements.txt

# Create directories
mkdir -p chroma_db logs

# Copy environment template
cp .env.example .env
# Edit .env with your credentials

# Run application
streamlit run streamlit_app.py --server.port 8501
```

## 🌐 Production Deployment

### Docker Deployment

Create `Dockerfile`:
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p chroma_db logs

# Expose port
EXPOSE 8501

# Health check
HEALTHCHECK CMD curl --fail http://localhost:8501/_stcore/health

# Run application
CMD ["streamlit", "run", "streamlit_app.py", "--server.port=8501", "--server.address=0.0.0.0"]
```

Create `docker-compose.yml`:
```yaml
version: '3.8'

services:
  azure-memory-chat:
    build: .
    ports:
      - "8501:8501"
    environment:
      - AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY}
      - AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT}
      - AZURE_OPENAI_DEPLOYMENT=${AZURE_OPENAI_DEPLOYMENT}
      - AZURE_OPENAI_API_VERSION=${AZURE_OPENAI_API_VERSION}
    volumes:
      - ./chroma_db:/app/chroma_db
      - ./logs:/app/logs
    restart: unless-stopped
```

Deploy with Docker:
```bash
# Build and run
docker-compose up -d

# View logs
docker-compose logs -f
```

### Cloud Deployment

#### Azure Container Instances
```bash
# Create resource group
az group create --name memory-chat-rg --location eastus

# Deploy container
az container create \
  --resource-group memory-chat-rg \
  --name azure-memory-chat \
  --image your-registry/azure-memory-chat:latest \
  --ports 8501 \
  --environment-variables \
    AZURE_OPENAI_API_KEY=$AZURE_OPENAI_API_KEY \
    AZURE_OPENAI_ENDPOINT=$AZURE_OPENAI_ENDPOINT \
    AZURE_OPENAI_DEPLOYMENT=$AZURE_OPENAI_DEPLOYMENT
```

#### Streamlit Cloud
1. Push code to GitHub repository
2. Connect to Streamlit Cloud
3. Add secrets in Streamlit Cloud dashboard:
   - `AZURE_OPENAI_API_KEY`
   - `AZURE_OPENAI_ENDPOINT`
   - `AZURE_OPENAI_DEPLOYMENT`

#### Heroku
Create `Procfile`:
```
web: streamlit run streamlit_app.py --server.port=$PORT --server.address=0.0.0.0
```

Deploy:
```bash
# Login to Heroku
heroku login

# Create app
heroku create your-app-name

# Set environment variables
heroku config:set AZURE_OPENAI_API_KEY=your-key
heroku config:set AZURE_OPENAI_ENDPOINT=your-endpoint
heroku config:set AZURE_OPENAI_DEPLOYMENT=your-deployment

# Deploy
git push heroku main
```

## 🔧 Configuration

### Environment Variables

| Variable | Required | Description | Default |
|----------|----------|-------------|---------|
| `AZURE_OPENAI_API_KEY` | ✅ | Azure OpenAI API key | - |
| `AZURE_OPENAI_ENDPOINT` | ✅ | Azure OpenAI endpoint URL | - |
| `AZURE_OPENAI_DEPLOYMENT` | ✅ | Deployment name | `gpt-35-turbo` |
| `AZURE_OPENAI_API_VERSION` | ❌ | API version | `2024-02-01` |
| `CHROMA_DB_PATH` | ❌ | Vector database path | `./chroma_db` |
| `SECRET_KEY` | ❌ | Session secret key | Auto-generated |

### Security Considerations

#### Production Security
- Change default `SECRET_KEY`
- Use HTTPS in production
- Implement rate limiting
- Set up proper logging
- Use environment variables for secrets
- Regular security updates

#### Data Protection
- Vector database is stored locally
- User passwords are hashed with bcrypt
- Session data is isolated per user
- No data sharing between users

## 📊 Monitoring

### Health Checks
```python
# Add to streamlit_app.py
import streamlit as st

def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

# Add route for health check
if st.query_params.get("health"):
    st.json(health_check())
    st.stop()
```

### Logging
```python
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/app.log'),
        logging.StreamHandler()
    ]
)
```

### Metrics
- User registration count
- Active sessions
- API response times
- Error rates
- Vector database size

## 🔄 Backup & Recovery

### Database Backup
```bash
# Backup ChromaDB
tar -czf chroma_backup_$(date +%Y%m%d).tar.gz chroma_db/

# Backup user database
cp users.json users_backup_$(date +%Y%m%d).json
```

### Automated Backup Script
```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup ChromaDB
tar -czf $BACKUP_DIR/chroma_$DATE.tar.gz chroma_db/

# Backup users
cp users.json $BACKUP_DIR/users_$DATE.json

# Keep only last 7 days of backups
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
find $BACKUP_DIR -name "users_*.json" -mtime +7 -delete

echo "Backup completed: $DATE"
```

## 🚨 Troubleshooting

### Common Issues

**Application won't start**
- Check Python version (3.11+ recommended)
- Verify all dependencies installed
- Check environment variables
- Review logs for errors

**Azure OpenAI connection fails**
- Verify API key is correct
- Check endpoint URL format
- Confirm deployment name exists
- Test API access independently

**Vector database errors**
- Check write permissions
- Verify ChromaDB installation
- Clear database if corrupted: `rm -rf chroma_db/`

**Memory issues**
- Monitor RAM usage
- Implement conversation pruning
- Use smaller embedding models
- Scale horizontally if needed

### Debug Mode
```bash
# Run with debug logging
STREAMLIT_LOGGER_LEVEL=debug streamlit run streamlit_app.py

# Check configuration
python -c "from config import get_config; print(get_config().validate_config())"
```

## 📈 Scaling

### Horizontal Scaling
- Use load balancer
- Shared database backend
- Session affinity
- Container orchestration

### Performance Optimization
- Cache embeddings
- Batch API requests
- Optimize vector search
- Use CDN for static assets

---

**Need help?** Check the main README.md for detailed usage instructions.
