#!/usr/bin/env python3
"""
Test script to check which Azure OpenAI deployments are available
"""

import os
import openai
from openai import AzureOpenAI

# Your Azure OpenAI configuration
API_KEY = "AVDB79YwEuh2OHb5VSNq5TTJjWDvJdqyBqzu3VR8Bnp8OyJjfx1eJQQJ99AKAC5RqLJXJ3w3AAABACOG3nTH"
BASE_URL = "https://a-cs--m30akd8h-westeurope.openai.azure.com"
API_VERSION = "2024-02-01"

# Common deployment names to test
COMMON_DEPLOYMENTS = [
    "gpt-4",
    "gpt-4o", 
    "gpt-4o-mini",
    "gpt-35-turbo",
    "gpt-3.5-turbo",
    "gpt4",
    "gpt4o",
    "text-davinci-003",
    "deployment-gpt4",
    "deployment-gpt4o"
]

def test_deployment(deployment_name):
    """Test if a deployment name works"""
    try:
        client = AzureOpenAI(
            api_key=API_KEY,
            api_version=API_VERSION,
            azure_endpoint=BASE_URL
        )
        
        response = client.chat.completions.create(
            model=deployment_name,
            messages=[{"role": "user", "content": "Hello, this is a test."}],
            max_tokens=10
        )
        
        return True, response.choices[0].message.content
    except Exception as e:
        return False, str(e)

def main():
    print("Testing Azure OpenAI deployments...")
    print(f"Endpoint: {BASE_URL}")
    print(f"API Version: {API_VERSION}")
    print("-" * 50)
    
    working_deployments = []
    
    for deployment in COMMON_DEPLOYMENTS:
        print(f"Testing deployment: {deployment}")
        success, result = test_deployment(deployment)
        
        if success:
            print(f"✅ SUCCESS: {deployment} works!")
            print(f"   Response: {result}")
            working_deployments.append(deployment)
        else:
            print(f"❌ FAILED: {deployment}")
            print(f"   Error: {result}")
        print()
    
    print("-" * 50)
    if working_deployments:
        print("✅ Working deployments found:")
        for deployment in working_deployments:
            print(f"   - {deployment}")
        print(f"\nUpdate your simple.py to use one of these deployment names.")
    else:
        print("❌ No working deployments found.")
        print("Please check:")
        print("1. Your API key is correct")
        print("2. Your Azure OpenAI endpoint URL is correct")
        print("3. You have created deployments in your Azure OpenAI resource")
        print("4. The deployment names in your Azure portal")

if __name__ == "__main__":
    main()
