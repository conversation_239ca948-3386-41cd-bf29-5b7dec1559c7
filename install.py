#!/usr/bin/env python3
"""
Installation script for Azure OpenAI Memory System
Installs required packages and sets up the environment.
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ All packages installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing packages: {e}")
        return False

def setup_environment():
    """Set up environment configuration"""
    print("\nSetting up environment...")
    
    if not os.path.exists(".env"):
        print("Creating .env file from template...")
        try:
            with open(".env.example", "r") as example:
                content = example.read()
            
            with open(".env", "w") as env_file:
                env_file.write(content)
            
            print("✅ .env file created!")
            print("⚠️  Please edit .env file with your Azure OpenAI credentials")
        except Exception as e:
            print(f"❌ Error creating .env file: {e}")
            return False
    else:
        print("✅ .env file already exists")
    
    return True

def create_directories():
    """Create necessary directories"""
    print("\nCreating directories...")
    
    directories = ["./chroma_db", "./logs"]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✅ Created directory: {directory}")
        else:
            print(f"✅ Directory already exists: {directory}")
    
    return True

def test_installation():
    """Test if installation is working"""
    print("\nTesting installation...")
    
    try:
        # Test imports
        import streamlit
        import chromadb
        import sentence_transformers
        import bcrypt
        import openai
        
        print("✅ All packages imported successfully!")
        
        # Test configuration
        from config import get_config
        config = get_config()
        validation = config.validate_config()
        
        print("\nConfiguration validation:")
        for key, valid in validation.items():
            status = "✅" if valid else "❌"
            print(f"  {status} {key}: {'Valid' if valid else 'Invalid'}")
        
        if not config.is_production_ready():
            print("\n⚠️  Configuration is not production ready. Please update your .env file.")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main installation function"""
    print("🚀 Azure OpenAI Memory System Installation")
    print("=" * 50)
    
    steps = [
        ("Installing packages", install_requirements),
        ("Setting up environment", setup_environment),
        ("Creating directories", create_directories),
        ("Testing installation", test_installation)
    ]
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        if not step_func():
            print(f"\n❌ Installation failed at: {step_name}")
            sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 Installation completed successfully!")
    print("\nNext steps:")
    print("1. Edit .env file with your Azure OpenAI credentials")
    print("2. Run: streamlit run streamlit_app.py")
    print("3. Open your browser to the displayed URL")
    print("\nEnjoy your Azure OpenAI Memory Chat! 🤖")

if __name__ == "__main__":
    main()
