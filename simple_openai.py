#!/usr/bin/env python3
"""
MemoryOS demo using regular OpenAI API instead of Azure
"""

import os
from memoryos import Memoryos

USER_ID = "demo_user"
ASSISTANT_ID = "demo_assistant"

# For regular OpenAI API - you would need a regular OpenAI API key
# OPENAI_API_KEY = "sk-your-openai-api-key-here"  # Replace with your OpenAI API key

DATA_STORAGE_PATH = "./simple_demo_data"
LLM_MODEL = "gpt-3.5-turbo"  # Standard OpenAI model name

def simple_demo():
    print("MemoryOS Simple Demo - Regular OpenAI API")
    
    # Uncomment and set your OpenAI API key
    # if not OPENAI_API_KEY.startswith("sk-"):
    #     print("❌ Please set your OpenAI API key in the script")
    #     return
    
    print("⚠️  This script requires a regular OpenAI API key")
    print("   If you only have Azure OpenAI, see the other solutions below.")
    return
    
    # 1. Initialize MemoryOS
    print("Initializing MemoryOS...")
    try:
        memo = Memoryos(
            user_id=USER_ID,
            openai_api_key=OPENAI_API_KEY,
            # No openai_base_url needed for regular OpenAI
            data_storage_path=DATA_STORAGE_PATH,
            llm_model=LLM_MODEL,
            assistant_id=ASSISTANT_ID,
            short_term_capacity=7,  
            mid_term_heat_threshold=5,  
            retrieval_queue_capacity=7,
            long_term_knowledge_capacity=100
        )
        print("MemoryOS initialized successfully!\n")
    except Exception as e:
        print(f"Error: {e}")
        return

    # 2. Add some basic memories
    print("Adding some memories...")
    
    memo.add_memory(
        user_input="Hi! I'm Tom, I work as a data scientist in San Francisco.",
        agent_response="Hello Tom! Nice to meet you. Data science is such an exciting field. What kind of data do you work with?"
    )
     
    test_query = "What do you remember about my job?"
    print(f"User: {test_query}")
    
    response = memo.get_response(
        query=test_query,
    )
    
    print(f"Assistant: {response}")

if __name__ == "__main__":
    simple_demo()
