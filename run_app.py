#!/usr/bin/env python3
"""
Startup script for Azure OpenAI Memory Chat
Handles environment setup and warning suppression
"""

import os
import sys
import warnings
import subprocess

def setup_environment():
    """Set up environment variables and suppress warnings"""

    # Suppress Python warnings
    os.environ["PYTHONWARNINGS"] = "ignore::UserWarning"

    # Suppress specific warnings
    warnings.filterwarnings("ignore", category=UserWarning)
    warnings.filterwarnings("ignore", message=".*torch.*")
    warnings.filterwarnings("ignore", message=".*_path.*")
    warnings.filterwarnings("ignore", message=".*RuntimeError.*")

    # Set Streamlit configuration
    os.environ["STREAMLIT_SERVER_HEADLESS"] = "true"
    os.environ["STREAMLIT_BROWSER_GATHER_USAGE_STATS"] = "false"
    os.environ["STREAMLIT_LOGGER_LEVEL"] = "error"

def check_dependencies():
    """Check if all required dependencies are installed"""
    try:
        import streamlit
        import chromadb
        import sentence_transformers
        import bcrypt
        import openai
        print("✅ All dependencies are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Please run: python install.py")
        return False

def run_streamlit():
    """Run the Streamlit application"""
    try:
        # Set up environment
        setup_environment()
        
        # Check dependencies
        if not check_dependencies():
            sys.exit(1)
        
        print("🚀 Starting Azure OpenAI Memory Chat...")
        print("📍 Application will be available at: http://localhost:8501")
        print("⚠️  Note: Some PyTorch warnings may appear but can be safely ignored")
        print()
        
        # Run Streamlit
        cmd = [
            sys.executable, "-m", "streamlit", "run", "streamlit_app.py",
            "--server.port", "8501",
            "--server.address", "0.0.0.0",
            "--server.headless", "true",
            "--browser.gatherUsageStats", "false",
            "--logger.level", "error"
        ]
        
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n👋 Shutting down Azure OpenAI Memory Chat...")
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        sys.exit(1)

if __name__ == "__main__":
    run_streamlit()
