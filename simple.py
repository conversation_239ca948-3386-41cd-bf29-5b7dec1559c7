
import os
from memoryos import Memoryos


USER_ID = "demo_user"
ASSISTANT_ID = "demo_assistant"
# API key for Azure OpenAI
API_KEY = "AVDB79YwEuh2OHb5VSNq5TTJjWDvJdqyBqzu3VR8Bnp8OyJjfx1eJQQJ99AKAC5RqLJXJ3w3AAABACOG3nTH"
# Azure OpenAI base URL (without deployment or API version)
BASE_URL = "https://a-cs--m30akd8h-westeurope.openai.azure.com"
# Azure OpenAI deployment name (you need to replace this with your actual deployment name)
# Common deployment names are: gpt-4, gpt-4o, gpt-35-turbo, etc.
# Check your Azure OpenAI resource in the Azure portal to see your deployment names
DEPLOYMENT_NAME = "gpt-35-turbo"  # Using working deployment name (more cost-effective)
DATA_STORAGE_PATH = "./simple_demo_data"
LLM_MODEL = DEPLOYMENT_NAME  # Use the deployment name directly for Azure

def simple_demo():
    print("MemoryOS Simple Demo")
    
    # 1. Initialize MemoryOS
    print("Initializing MemoryOS...")
    print(f"Using Azure OpenAI endpoint: {BASE_URL}")
    print(f"Using deployment name: {DEPLOYMENT_NAME}")
    print("Note: MemoryOS may still try to use gpt-4o-mini for some operations")
    print(f"API version: 2024-02-01")
    try:
        # Set environment variables for Azure OpenAI
        os.environ["OPENAI_API_TYPE"] = "azure"
        os.environ["OPENAI_API_VERSION"] = "2024-02-01"  # Updated to a more recent API version
        os.environ["OPENAI_API_BASE"] = BASE_URL
        os.environ["OPENAI_API_KEY"] = API_KEY
        
        memo = Memoryos(
            user_id=USER_ID,
            openai_api_key=API_KEY,
            openai_base_url=BASE_URL,
            data_storage_path=DATA_STORAGE_PATH,
            llm_model=LLM_MODEL,
            assistant_id=ASSISTANT_ID,
            short_term_capacity=7,  
            mid_term_heat_threshold=5,  
            retrieval_queue_capacity=7,
            long_term_knowledge_capacity=100
        )
        print("MemoryOS initialized successfully!\n")
    except Exception as e:
        print(f"Error: {e}")
        return

    # 2. Add some basic memories
    print("Adding some memories...")
    
    memo.add_memory(
        user_input="Hi! I'm Tom, I work as a data scientist in San Francisco.",
        agent_response="Hello Tom! Nice to meet you. Data science is such an exciting field. What kind of data do you work with?"
    )
     
    test_query = "What do you remember about my job?"
    print(f"User: {test_query}")
    
    response = memo.get_response(
        query=test_query,
    )
    
    print(f"Assistant: {response}")

if __name__ == "__main__":
    simple_demo()
