#!/usr/bin/env python3
"""
Simple memory system that works with Azure OpenAI
"""

import os
import json
from datetime import datetime
from openai import AzureOpenAI

class SimpleMemorySystem:
    def __init__(self, api_key, azure_endpoint, deployment_name, api_version="2024-02-01"):
        self.client = AzureOpenAI(
            api_key=api_key,
            api_version=api_version,
            azure_endpoint=azure_endpoint
        )
        self.deployment_name = deployment_name
        self.memory_file = "simple_memory.json"
        self.memory = self.load_memory()
    
    def load_memory(self):
        """Load memory from file"""
        if os.path.exists(self.memory_file):
            with open(self.memory_file, 'r') as f:
                return json.load(f)
        return {"conversations": [], "user_profile": ""}
    
    def save_memory(self):
        """Save memory to file"""
        with open(self.memory_file, 'w') as f:
            json.dump(self.memory, f, indent=2)
    
    def add_conversation(self, user_input, assistant_response):
        """Add a conversation to memory"""
        conversation = {
            "timestamp": datetime.now().isoformat(),
            "user": user_input,
            "assistant": assistant_response
        }
        self.memory["conversations"].append(conversation)
        
        # Keep only last 10 conversations to avoid token limits
        if len(self.memory["conversations"]) > 10:
            self.memory["conversations"] = self.memory["conversations"][-10:]
        
        self.save_memory()
        print(f"💾 Saved conversation to memory")
    
    def get_context(self):
        """Get conversation context for the AI"""
        context = "Previous conversations:\n"
        for conv in self.memory["conversations"][-5:]:  # Last 5 conversations
            context += f"User: {conv['user']}\nAssistant: {conv['assistant']}\n\n"
        
        if self.memory["user_profile"]:
            context += f"User profile: {self.memory['user_profile']}\n\n"
        
        return context
    
    def generate_response(self, user_input):
        """Generate a response using Azure OpenAI"""
        try:
            context = self.get_context()
            
            messages = [
                {"role": "system", "content": "You are a helpful assistant with memory of previous conversations. Use the context provided to give personalized responses."},
                {"role": "user", "content": f"Context:\n{context}\n\nCurrent question: {user_input}"}
            ]
            
            print(f"🤖 Calling Azure OpenAI (deployment: {self.deployment_name})...")
            response = self.client.chat.completions.create(
                model=self.deployment_name,
                messages=messages,
                max_tokens=500,
                temperature=0.7
            )
            
            assistant_response = response.choices[0].message.content
            
            # Save this conversation
            self.add_conversation(user_input, assistant_response)
            
            return assistant_response
            
        except Exception as e:
            error_msg = f"Error: {e}"
            print(f"❌ {error_msg}")
            return error_msg
    
    def update_user_profile(self, profile_info):
        """Update user profile information"""
        self.memory["user_profile"] = profile_info
        self.save_memory()
        print(f"👤 Updated user profile")

def main():
    print("Simple Memory System Demo")
    print("=" * 40)
    
    # Configuration
    API_KEY = "AVDB79YwEuh2OHb5VSNq5TTJjWDvJdqyBqzu3VR8Bnp8OyJjfx1eJQQJ99AKAC5RqLJXJ3w3AAABACOG3nTH"
    AZURE_ENDPOINT = "https://a-cs--m30akd8h-westeurope.openai.azure.com"
    DEPLOYMENT_NAME = "gpt-35-turbo"  # Using the working deployment
    
    # Initialize memory system
    memory_system = SimpleMemorySystem(
        api_key=API_KEY,
        azure_endpoint=AZURE_ENDPOINT,
        deployment_name=DEPLOYMENT_NAME
    )
    
    print(f"✅ Memory system initialized")
    print(f"📍 Using deployment: {DEPLOYMENT_NAME}")
    print()
    
    # Add some initial information
    memory_system.update_user_profile("Tom, data scientist from San Francisco")
    
    # Simulate a conversation
    print("🗣️  Starting conversation...")
    print()
    
    # First interaction
    user_input1 = "Hi! I'm Tom, I work as a data scientist in San Francisco."
    print(f"User: {user_input1}")
    response1 = memory_system.generate_response(user_input1)
    print(f"Assistant: {response1}")
    print()
    
    # Second interaction - testing memory
    user_input2 = "What do you remember about my job?"
    print(f"User: {user_input2}")
    response2 = memory_system.generate_response(user_input2)
    print(f"Assistant: {response2}")
    print()
    
    # Third interaction
    user_input3 = "I'm working on a machine learning project with customer data."
    print(f"User: {user_input3}")
    response3 = memory_system.generate_response(user_input3)
    print(f"Assistant: {response3}")
    print()
    
    # Fourth interaction - testing memory of recent conversation
    user_input4 = "What kind of project did I mention?"
    print(f"User: {user_input4}")
    response4 = memory_system.generate_response(user_input4)
    print(f"Assistant: {response4}")
    
    print("\n" + "=" * 40)
    print("✅ Demo completed! Check 'simple_memory.json' for saved conversations.")

if __name__ == "__main__":
    main()
