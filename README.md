# Azure OpenAI Memory System

A simple but effective memory system that works with Azure OpenAI, providing conversation history, user profiles, and context-aware responses.

## Features

✅ **Azure OpenAI Integration** - Works seamlessly with Azure OpenAI deployments  
✅ **Conversation Memory** - Remembers previous conversations  
✅ **User Profiles** - Maintains user information across sessions  
✅ **Persistent Storage** - Saves conversations to JSON file  
✅ **Context-Aware Responses** - Uses conversation history for better responses  
✅ **Error Handling** - Graceful handling of API errors  

## Quick Start

1. **Configure your Azure OpenAI details** in `azure_memory_system.py`:
   ```python
   AZURE_CONFIG = {
       "api_key": "your-azure-openai-api-key",
       "azure_endpoint": "https://your-resource.openai.azure.com",
       "deployment_name": "your-deployment-name",
       "api_version": "2024-02-01"
   }
   ```

2. **Run the demo**:
   ```bash
   python azure_memory_system.py
   ```

3. **Use in your own code**:
   ```python
   from azure_memory_system import AzureMemorySystem
   
   # Initialize
   memory = AzureMemorySystem(
       api_key="your-key",
       azure_endpoint="your-endpoint", 
       deployment_name="your-deployment"
   )
   
   # Add conversation
   response = memory.generate_response("Hello, I'm John!")
   print(response)
   
   # Memory persists across calls
   response = memory.generate_response("What's my name?")
   print(response)  # Will remember "John"
   ```

## Files

- `azure_memory_system.py` - Main memory system implementation
- `memory.json` - Conversation history storage (auto-created)
- `README.md` - This documentation

## Configuration

The system automatically:
- Keeps the last 10 conversations in memory
- Uses the last 5 conversations for context
- Saves all conversations to `memory.json`
- Handles API errors gracefully

## Requirements

- Python 3.7+
- `openai` package
- Azure OpenAI resource with a deployed model

## Installation

```bash
pip install openai
```

## Troubleshooting

**404 Errors**: Check that your deployment name matches exactly what's in your Azure OpenAI resource.

**401 Errors**: Verify your API key is correct.

**Connection Issues**: Ensure your Azure endpoint URL is correct.

## Example Output

```
Azure OpenAI Memory System Demo
=============================================
✅ Memory system initialized with deployment: gpt-35-turbo
📍 Using deployment: gpt-35-turbo

👤 Updated user profile
🗣️  Starting conversation...

User: Hi! I'm Tom, I work as a data scientist in San Francisco.
🤖 Calling Azure OpenAI (deployment: gpt-35-turbo)...
💾 Saved conversation to memory
Assistant: Hello, Tom! It's great to meet you. How can I assist you today?

User: What do you remember about my job?
🤖 Calling Azure OpenAI (deployment: gpt-35-turbo)...
💾 Saved conversation to memory
Assistant: You work as a data scientist in San Francisco...
```

---

*This system was created as a working alternative to MemoryOS for Azure OpenAI environments.*
