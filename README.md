# Azure OpenAI Memory Chat - Streamlit Web Application

A comprehensive web-based chatbot with Azure OpenAI integration, vector database storage, user authentication, and persistent conversation memory.

## 🌟 Features

### 🤖 **Advanced AI Chat**
- Azure OpenAI integration with context-aware responses
- Semantic conversation memory using vector embeddings
- Personalized responses based on conversation history
- Real-time typing indicators and smooth UI

### 🔐 **Security & Authentication**
- User registration and login system
- Password hashing with bcrypt
- Session management with Streamlit
- User data isolation and privacy protection

### 🗄️ **Vector Database Storage**
- ChromaDB for scalable conversation storage
- Semantic search for relevant context retrieval
- User profile management and preferences
- Conversation embeddings for better context matching

### 🎨 **Modern Web Interface**
- Clean, responsive Streamlit interface
- Real-time chat with message history
- User dashboard with statistics
- Easy conversation management

## 🚀 Quick Start

### 1. Installation

```bash
# Clone or download the project
cd azure-openai-memory-chat

# Run the installation script
python install.py
```

### 2. Configuration

Edit the `.env` file with your Azure OpenAI credentials:

```env
AZURE_OPENAI_API_KEY=your-azure-openai-api-key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com
AZURE_OPENAI_DEPLOYMENT=your-deployment-name
AZURE_OPENAI_API_VERSION=2024-02-01
```

### 3. Run the Application

```bash
streamlit run streamlit_app.py
```

### 4. Access the Web Interface

Open your browser to the URL shown in the terminal (usually `http://localhost:8501`)

## 📁 Project Structure

```
azure-openai-memory-chat/
├── streamlit_app.py          # Main Streamlit application
├── memory_manager.py         # Vector database memory management
├── auth_manager.py           # User authentication system
├── azure_client.py           # Azure OpenAI API client
├── config.py                 # Configuration management
├── install.py                # Installation script
├── requirements.txt          # Python dependencies
├── .env.example             # Environment variables template
├── README.md                # This documentation
└── chroma_db/               # Vector database storage (auto-created)
```

## 🔧 Configuration Options

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `AZURE_OPENAI_API_KEY` | Your Azure OpenAI API key | Required |
| `AZURE_OPENAI_ENDPOINT` | Azure OpenAI endpoint URL | Required |
| `AZURE_OPENAI_DEPLOYMENT` | Deployment name | `gpt-35-turbo` |
| `AZURE_OPENAI_API_VERSION` | API version | `2024-02-01` |
| `CHROMA_DB_PATH` | Vector database path | `./chroma_db` |
| `MAX_CONVERSATION_HISTORY` | Max conversations to store | `50` |
| `MAX_CONTEXT_RESULTS` | Max context results for AI | `5` |

### Customization

You can customize the application by modifying:
- `config.py` - Application settings
- `streamlit_app.py` - UI components and layout
- `memory_manager.py` - Memory storage behavior
- `azure_client.py` - AI response generation

## 🎯 Usage Guide

### First Time Setup

1. **Register an Account**: Create a username and password
2. **Start Chatting**: Begin conversations with the AI assistant
3. **Memory Building**: The system automatically remembers your conversations
4. **Context Awareness**: Ask follow-up questions and see how the AI remembers context

### Features in Action

- **Conversation Memory**: "What did we discuss yesterday?"
- **Personal Context**: "Remember, I'm a data scientist in San Francisco"
- **Topic Continuity**: "Continue our discussion about machine learning"
- **User Profiles**: The system builds a profile based on your conversations

### Managing Your Data

- **Clear History**: Use the sidebar button to clear conversation history
- **User Stats**: View your conversation statistics in the sidebar
- **Logout**: Securely end your session

## 🛡️ Security Features

### Data Protection
- **Password Hashing**: Passwords are securely hashed with bcrypt
- **User Isolation**: Each user's data is completely isolated
- **Session Management**: Secure session handling with Streamlit
- **Vector Database**: Encrypted storage in ChromaDB

### Privacy
- **Local Storage**: All data stored locally (no external services)
- **User Control**: Users can clear their own data anytime
- **No Data Sharing**: Conversations are never shared between users

## 🔍 Troubleshooting

### Common Issues

**"Error generating response"**
- Check your Azure OpenAI API key in `.env`
- Verify your deployment name is correct
- Ensure your Azure OpenAI resource is active

**"Authentication failed"**
- Check username/password combination
- Try registering a new account
- Clear browser cache if needed

**"Vector database error"**
- Ensure write permissions in the project directory
- Check if `chroma_db` directory exists
- Restart the application

### Getting Help

1. Check the configuration with: `python -c "from config import get_config; print(get_config().validate_config())"`
2. Test Azure OpenAI connection independently
3. Check Streamlit logs for detailed error messages

## 📊 Technical Details

### Architecture
- **Frontend**: Streamlit web interface
- **Backend**: Python with Azure OpenAI API
- **Database**: ChromaDB vector database
- **Authentication**: bcrypt password hashing
- **Embeddings**: Sentence Transformers for semantic search

### Performance
- **Response Time**: Typically 1-3 seconds for AI responses
- **Memory Efficiency**: Automatic conversation pruning
- **Scalability**: Supports multiple concurrent users
- **Storage**: Efficient vector storage with ChromaDB

### Dependencies
- `streamlit>=1.28.0` - Web interface
- `chromadb>=0.4.0` - Vector database
- `sentence-transformers>=2.2.0` - Text embeddings
- `openai>=1.0.0` - Azure OpenAI API
- `bcrypt>=4.0.0` - Password hashing

## 🤝 Contributing

This is a complete, production-ready application. Feel free to:
- Customize the UI in `streamlit_app.py`
- Add new features to the memory system
- Improve the authentication system
- Enhance the vector database functionality

## 📄 License

This project is provided as-is for educational and commercial use.

---

**Built with ❤️ for Azure OpenAI and Streamlit communities**
