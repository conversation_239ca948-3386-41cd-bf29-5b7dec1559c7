#!/usr/bin/env python3
"""
Azure OpenAI Client for Memory System
Handles Azure OpenAI API interactions with context-aware responses.
"""

import os
from openai import AzureOpenAI
from typing import List, Dict, Optional
import streamlit as st
from datetime import datetime
from config import get_config

class AzureOpenAIClient:
    """Azure OpenAI client with memory integration"""
    
    def __init__(self):
        """Initialize Azure OpenAI client"""
        # Get configuration
        config = get_config()
        self.config = config.get_azure_config()
        
        # Initialize Azure OpenAI client
        self.client = AzureOpenAI(
            api_key=self.config["api_key"],
            api_version=self.config["api_version"],
            azure_endpoint=self.config["azure_endpoint"]
        )
        
        self.deployment_name = self.config["deployment_name"]
    
    def generate_response(self, 
                         user_message: str, 
                         context: List[Dict], 
                         user_id: str,
                         max_tokens: int = 1000,
                         temperature: float = 0.7) -> str:
        """
        Generate a response using Azure OpenAI with conversation context.
        
        Args:
            user_message: Current user message
            context: Relevant conversation context from memory
            user_id: User identifier
            max_tokens: Maximum tokens in response
            temperature: Response creativity (0.0 to 1.0)
            
        Returns:
            Generated response
        """
        try:
            # Build context string from memory
            context_str = self._build_context_string(context)
            
            # Create system message with context
            system_message = self._create_system_message(context_str, user_id)
            
            # Prepare messages for API call
            messages = [
                {"role": "system", "content": system_message},
                {"role": "user", "content": user_message}
            ]
            
            # Add recent conversation context as messages
            messages.extend(self._format_context_as_messages(context))
            
            # Call Azure OpenAI API
            response = self.client.chat.completions.create(
                model=self.deployment_name,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                top_p=0.9,
                frequency_penalty=0.1,
                presence_penalty=0.1
            )
            
            return response.choices[0].message.content.strip()
        
        except Exception as e:
            error_msg = f"Error generating response: {str(e)}"
            st.error(error_msg)
            return "I apologize, but I'm having trouble generating a response right now. Please try again."
    
    def _build_context_string(self, context: List[Dict]) -> str:
        """Build context string from conversation history"""
        if not context:
            return "No previous conversation history available."
        
        context_parts = []
        context_parts.append("=== RELEVANT CONVERSATION HISTORY ===")
        
        for i, conv in enumerate(context[:5]):  # Limit to 5 most relevant
            timestamp = conv.get('timestamp', 'Unknown time')
            user_msg = conv.get('user_message', '')
            assistant_msg = conv.get('assistant_message', '')
            relevance = conv.get('relevance_score', 0)
            
            context_parts.append(f"\n--- Conversation {i+1} (Relevance: {relevance:.2f}) ---")
            context_parts.append(f"Time: {timestamp}")
            context_parts.append(f"User: {user_msg}")
            context_parts.append(f"Assistant: {assistant_msg}")
        
        context_parts.append("\n=== END CONVERSATION HISTORY ===")
        return "\n".join(context_parts)
    
    def _create_system_message(self, context_str: str, user_id: str) -> str:
        """Create system message with context and instructions"""
        return f"""You are a helpful AI assistant with access to conversation history. 

IMPORTANT INSTRUCTIONS:
1. Use the conversation history below to provide personalized, context-aware responses
2. Remember details about the user from previous conversations
3. Be consistent with information you've shared before
4. If you don't have relevant context, acknowledge this naturally
5. Be conversational and helpful
6. Maintain continuity across conversations

USER ID: {user_id[:8]}... (for your reference only - don't mention this to the user)

{context_str}

Based on this context, respond to the user's current message in a helpful and personalized way."""
    
    def _format_context_as_messages(self, context: List[Dict]) -> List[Dict]:
        """Format context as conversation messages (limited to most recent/relevant)"""
        messages = []
        
        # Add only the most recent 2-3 conversations to avoid token limits
        for conv in context[:3]:
            user_msg = conv.get('user_message', '')
            assistant_msg = conv.get('assistant_message', '')
            
            if user_msg and assistant_msg:
                messages.append({"role": "user", "content": user_msg})
                messages.append({"role": "assistant", "content": assistant_msg})
        
        return messages
    
    def generate_summary(self, conversations: List[Dict]) -> str:
        """Generate a summary of conversations for user profile"""
        try:
            if not conversations:
                return "No conversations to summarize."
            
            # Prepare conversation text for summarization
            conv_text = []
            for conv in conversations[-10:]:  # Last 10 conversations
                user_msg = conv.get('user_message', '')
                assistant_msg = conv.get('assistant_message', '')
                conv_text.append(f"User: {user_msg}\nAssistant: {assistant_msg}")
            
            combined_text = "\n\n".join(conv_text)
            
            messages = [
                {
                    "role": "system", 
                    "content": "Summarize the key topics, interests, and important information about the user from these conversations. Be concise but comprehensive."
                },
                {
                    "role": "user", 
                    "content": f"Please summarize these conversations:\n\n{combined_text}"
                }
            ]
            
            response = self.client.chat.completions.create(
                model=self.deployment_name,
                messages=messages,
                max_tokens=300,
                temperature=0.3
            )
            
            return response.choices[0].message.content.strip()
        
        except Exception as e:
            st.error(f"Error generating summary: {e}")
            return "Unable to generate summary."
    
    def extract_topics(self, text: str) -> List[str]:
        """Extract key topics from text"""
        try:
            messages = [
                {
                    "role": "system",
                    "content": "Extract 3-5 key topics or themes from the given text. Return them as a comma-separated list."
                },
                {
                    "role": "user",
                    "content": f"Extract key topics from: {text}"
                }
            ]
            
            response = self.client.chat.completions.create(
                model=self.deployment_name,
                messages=messages,
                max_tokens=100,
                temperature=0.3
            )
            
            topics_text = response.choices[0].message.content.strip()
            topics = [topic.strip() for topic in topics_text.split(',')]
            return topics[:5]  # Limit to 5 topics
        
        except Exception as e:
            st.error(f"Error extracting topics: {e}")
            return []
    
    def check_connection(self) -> bool:
        """Test Azure OpenAI connection"""
        try:
            self.client.chat.completions.create(
                model=self.deployment_name,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=10
            )
            return True
        except Exception as e:
            st.error(f"Azure OpenAI connection failed: {e}")
            return False
    
    def get_model_info(self) -> Dict:
        """Get information about the current model"""
        return {
            "deployment_name": self.deployment_name,
            "api_version": self.config["api_version"],
            "endpoint": self.config["azure_endpoint"],
            "status": "Connected" if self.check_connection() else "Disconnected"
        }
