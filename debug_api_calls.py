#!/usr/bin/env python3
"""
Debug script to test different ways of calling Azure OpenAI API
"""

import os
import openai
from openai import AzureOpenAI

# Configuration
API_KEY = "AVDB79YwEuh2OHb5VSNq5TTJjWDvJdqyBqzu3VR8Bnp8OyJjfx1eJQQJ99AKAC5RqLJXJ3w3AAABACOG3nTH"
BASE_URL = "https://a-cs--m30akd8h-westeurope.openai.azure.com"
API_VERSION = "2024-02-01"

def test_method_1_direct():
    """Test Method 1: Direct AzureOpenAI client (like our test script)"""
    print("=== Method 1: Direct AzureOpenAI client ===")
    try:
        client = AzureOpenAI(
            api_key=API_KEY,
            api_version=API_VERSION,
            azure_endpoint=BASE_URL
        )
        
        response = client.chat.completions.create(
            model="gpt-35-turbo",
            messages=[{"role": "user", "content": "Hello, this is a test."}],
            max_tokens=10
        )
        
        print("✅ SUCCESS")
        print(f"Response: {response.choices[0].message.content}")
        return True
    except Exception as e:
        print(f"❌ FAILED: {e}")
        return False

def test_method_2_env_vars():
    """Test Method 2: Using environment variables (like MemoryOS might)"""
    print("\n=== Method 2: Using environment variables ===")
    try:
        # Set environment variables like MemoryOS does
        os.environ["OPENAI_API_TYPE"] = "azure"
        os.environ["OPENAI_API_VERSION"] = API_VERSION
        os.environ["OPENAI_API_BASE"] = BASE_URL
        os.environ["OPENAI_API_KEY"] = API_KEY
        
        # Try using the openai module directly
        response = openai.chat.completions.create(
            model="gpt-35-turbo",
            messages=[{"role": "user", "content": "Hello, this is a test."}],
            max_tokens=10
        )
        
        print("✅ SUCCESS")
        print(f"Response: {response.choices[0].message.content}")
        return True
    except Exception as e:
        print(f"❌ FAILED: {e}")
        return False

def test_method_3_azure_endpoint():
    """Test Method 3: Different endpoint format"""
    print("\n=== Method 3: Different endpoint format ===")
    try:
        # Try with /openai/ suffix
        endpoint_with_suffix = BASE_URL + "/openai/"
        
        client = AzureOpenAI(
            api_key=API_KEY,
            api_version=API_VERSION,
            azure_endpoint=endpoint_with_suffix
        )
        
        response = client.chat.completions.create(
            model="gpt-35-turbo",
            messages=[{"role": "user", "content": "Hello, this is a test."}],
            max_tokens=10
        )
        
        print("✅ SUCCESS")
        print(f"Response: {response.choices[0].message.content}")
        print(f"Working endpoint: {endpoint_with_suffix}")
        return True
    except Exception as e:
        print(f"❌ FAILED: {e}")
        return False

def test_method_4_openai_client():
    """Test Method 4: Using openai.OpenAI with azure base_url"""
    print("\n=== Method 4: OpenAI client with Azure base_url ===")
    try:
        # This is how some libraries might call Azure OpenAI
        client = openai.OpenAI(
            api_key=API_KEY,
            base_url=f"{BASE_URL}/openai/deployments/gpt-35-turbo",
            default_headers={"api-version": API_VERSION}
        )
        
        response = client.chat.completions.create(
            model="gpt-35-turbo",  # This might be ignored when using deployment URL
            messages=[{"role": "user", "content": "Hello, this is a test."}],
            max_tokens=10
        )
        
        print("✅ SUCCESS")
        print(f"Response: {response.choices[0].message.content}")
        return True
    except Exception as e:
        print(f"❌ FAILED: {e}")
        return False

def main():
    print("Testing different Azure OpenAI API call methods...")
    print(f"Base URL: {BASE_URL}")
    print(f"API Version: {API_VERSION}")
    print("=" * 60)
    
    methods = [
        test_method_1_direct,
        test_method_2_env_vars,
        test_method_3_azure_endpoint,
        test_method_4_openai_client
    ]
    
    working_methods = []
    
    for method in methods:
        if method():
            working_methods.append(method.__name__)
    
    print("\n" + "=" * 60)
    print("SUMMARY:")
    if working_methods:
        print("✅ Working methods:")
        for method in working_methods:
            print(f"   - {method}")
    else:
        print("❌ No methods worked")
        print("Please check your Azure OpenAI configuration")

if __name__ == "__main__":
    main()
