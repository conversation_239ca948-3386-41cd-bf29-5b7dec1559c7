#!/usr/bin/env python3
"""
Azure OpenAI Memory System
A simple but effective memory system that works with Azure OpenAI.
Provides conversation history, user profiles, and context-aware responses.
"""

import os
import json
from datetime import datetime
from openai import AzureOpenAI

# Configuration - Update these with your Azure OpenAI details
AZURE_CONFIG = {
    "api_key": "AVDB79YwEuh2OHb5VSNq5TTJjWDvJdqyBqzu3VR8Bnp8OyJjfx1eJQQJ99AKAC5RqLJXJ3w3AAABACOG3nTH",
    "azure_endpoint": "https://a-cs--m30akd8h-westeurope.openai.azure.com",
    "deployment_name": "gpt-35-turbo",  # Change to your deployment name
    "api_version": "2024-02-01"
}

class AzureMemorySystem:
    """
    A memory system for Azure OpenAI that maintains conversation history and user profiles.
    """

    def __init__(self, api_key, azure_endpoint, deployment_name, api_version="2024-02-01", memory_file="memory.json"):
        """
        Initialize the memory system.

        Args:
            api_key: Azure OpenAI API key
            azure_endpoint: Azure OpenAI endpoint URL
            deployment_name: Name of your Azure OpenAI deployment
            api_version: API version (default: "2024-02-01")
            memory_file: File to store conversation history (default: "memory.json")
        """
        self.client = AzureOpenAI(
            api_key=api_key,
            api_version=api_version,
            azure_endpoint=azure_endpoint
        )
        self.deployment_name = deployment_name
        self.memory_file = memory_file
        self.memory = self.load_memory()
        print(f"✅ Memory system initialized with deployment: {deployment_name}")
    
    def load_memory(self):
        """Load memory from file"""
        if os.path.exists(self.memory_file):
            with open(self.memory_file, 'r') as f:
                return json.load(f)
        return {"conversations": [], "user_profile": ""}
    
    def save_memory(self):
        """Save memory to file"""
        with open(self.memory_file, 'w') as f:
            json.dump(self.memory, f, indent=2)
    
    def add_conversation(self, user_input, assistant_response):
        """Add a conversation to memory"""
        conversation = {
            "timestamp": datetime.now().isoformat(),
            "user": user_input,
            "assistant": assistant_response
        }
        self.memory["conversations"].append(conversation)
        
        # Keep only last 10 conversations to avoid token limits
        if len(self.memory["conversations"]) > 10:
            self.memory["conversations"] = self.memory["conversations"][-10:]
        
        self.save_memory()
        print(f"💾 Saved conversation to memory")
    
    def get_context(self):
        """Get conversation context for the AI"""
        context = "Previous conversations:\n"
        for conv in self.memory["conversations"][-5:]:  # Last 5 conversations
            context += f"User: {conv['user']}\nAssistant: {conv['assistant']}\n\n"
        
        if self.memory["user_profile"]:
            context += f"User profile: {self.memory['user_profile']}\n\n"
        
        return context
    
    def generate_response(self, user_input):
        """Generate a response using Azure OpenAI"""
        try:
            context = self.get_context()
            
            messages = [
                {"role": "system", "content": "You are a helpful assistant with memory of previous conversations. Use the context provided to give personalized responses."},
                {"role": "user", "content": f"Context:\n{context}\n\nCurrent question: {user_input}"}
            ]
            
            print(f"🤖 Calling Azure OpenAI (deployment: {self.deployment_name})...")
            response = self.client.chat.completions.create(
                model=self.deployment_name,
                messages=messages,
                max_tokens=500,
                temperature=0.7
            )
            
            assistant_response = response.choices[0].message.content
            
            # Save this conversation
            self.add_conversation(user_input, assistant_response)
            
            return assistant_response
            
        except Exception as e:
            error_msg = f"Error: {e}"
            print(f"❌ {error_msg}")
            return error_msg
    
    def update_user_profile(self, profile_info):
        """Update user profile information"""
        self.memory["user_profile"] = profile_info
        self.save_memory()
        print(f"👤 Updated user profile")

def main():
    print("Azure OpenAI Memory System Demo")
    print("=" * 45)

    # Initialize memory system using configuration
    memory_system = AzureMemorySystem(**AZURE_CONFIG)
    
    print(f"📍 Using deployment: {AZURE_CONFIG['deployment_name']}")
    print()
    
    # Add some initial information
    memory_system.update_user_profile("Tom, data scientist from San Francisco")
    
    # Simulate a conversation
    print("🗣️  Starting conversation...")
    print()
    
    # First interaction
    user_input1 = "Hi! I'm Tom, I work as a data scientist in San Francisco."
    print(f"User: {user_input1}")
    response1 = memory_system.generate_response(user_input1)
    print(f"Assistant: {response1}")
    print()
    
    # Second interaction - testing memory
    user_input2 = "What do you remember about my job?"
    print(f"User: {user_input2}")
    response2 = memory_system.generate_response(user_input2)
    print(f"Assistant: {response2}")
    print()
    
    # Third interaction
    user_input3 = "I'm working on a machine learning project with customer data."
    print(f"User: {user_input3}")
    response3 = memory_system.generate_response(user_input3)
    print(f"Assistant: {response3}")
    print()
    
    # Fourth interaction - testing memory of recent conversation
    user_input4 = "What kind of project did I mention?"
    print(f"User: {user_input4}")
    response4 = memory_system.generate_response(user_input4)
    print(f"Assistant: {response4}")
    
    print("\n" + "=" * 45)
    print("✅ Demo completed! Check 'memory.json' for saved conversations.")

if __name__ == "__main__":
    main()
