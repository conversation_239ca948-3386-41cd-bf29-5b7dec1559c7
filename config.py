#!/usr/bin/env python3
"""
Configuration Manager for Azure OpenAI Memory System
Handles application configuration and environment variables.
"""

import os
from typing import Dict, Any
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    """Application configuration"""
    
    # Azure OpenAI Configuration
    AZURE_OPENAI_API_KEY = os.getenv(
        "AZURE_OPENAI_API_KEY", 
        "AVDB79YwEuh2OHb5VSNq5TTJjWDvJdqyBqzu3VR8Bnp8OyJjfx1eJQQJ99AKAC5RqLJXJ3w3AAABACOG3nTH"
    )
    AZURE_OPENAI_ENDPOINT = os.getenv(
        "AZURE_OPENAI_ENDPOINT", 
        "https://a-cs--m30akd8h-westeurope.openai.azure.com"
    )
    AZURE_OPENAI_DEPLOYMENT = os.getenv("AZURE_OPENAI_DEPLOYMENT", "gpt-35-turbo")
    AZURE_OPENAI_API_VERSION = os.getenv("AZURE_OPENAI_API_VERSION", "2024-02-01")
    
    # Database Configuration
    CHROMA_DB_PATH = os.getenv("CHROMA_DB_PATH", "./chroma_db")
    USERS_DB_PATH = os.getenv("USERS_DB_PATH", "./users.json")
    
    # Security Configuration
    SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-change-this-in-production")
    
    # Application Configuration
    APP_TITLE = os.getenv("APP_TITLE", "Azure OpenAI Memory Chat")
    MAX_CONVERSATION_HISTORY = int(os.getenv("MAX_CONVERSATION_HISTORY", "50"))
    MAX_CONTEXT_RESULTS = int(os.getenv("MAX_CONTEXT_RESULTS", "5"))
    
    # Embedding Model Configuration
    EMBEDDING_MODEL = os.getenv("EMBEDDING_MODEL", "all-MiniLM-L6-v2")
    
    # Chat Configuration
    DEFAULT_MAX_TOKENS = int(os.getenv("DEFAULT_MAX_TOKENS", "1000"))
    DEFAULT_TEMPERATURE = float(os.getenv("DEFAULT_TEMPERATURE", "0.7"))
    
    @classmethod
    def get_azure_config(cls) -> Dict[str, Any]:
        """Get Azure OpenAI configuration"""
        return {
            "api_key": cls.AZURE_OPENAI_API_KEY,
            "azure_endpoint": cls.AZURE_OPENAI_ENDPOINT,
            "deployment_name": cls.AZURE_OPENAI_DEPLOYMENT,
            "api_version": cls.AZURE_OPENAI_API_VERSION
        }
    
    @classmethod
    def get_db_config(cls) -> Dict[str, Any]:
        """Get database configuration"""
        return {
            "chroma_db_path": cls.CHROMA_DB_PATH,
            "users_db_path": cls.USERS_DB_PATH
        }
    
    @classmethod
    def validate_config(cls) -> Dict[str, bool]:
        """Validate configuration"""
        validation = {
            "azure_api_key": bool(cls.AZURE_OPENAI_API_KEY and cls.AZURE_OPENAI_API_KEY != "your-azure-openai-api-key-here"),
            "azure_endpoint": bool(cls.AZURE_OPENAI_ENDPOINT and cls.AZURE_OPENAI_ENDPOINT.startswith("https://")),
            "deployment_name": bool(cls.AZURE_OPENAI_DEPLOYMENT),
            "secret_key": bool(cls.SECRET_KEY and cls.SECRET_KEY != "your-secret-key-change-this-in-production")
        }
        return validation
    
    @classmethod
    def is_production_ready(cls) -> bool:
        """Check if configuration is ready for production"""
        validation = cls.validate_config()
        return all(validation.values())

# Development/Testing Configuration
class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    TESTING = False

# Production Configuration  
class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    TESTING = False

# Get configuration based on environment
def get_config() -> Config:
    """Get configuration based on environment"""
    env = os.getenv("ENVIRONMENT", "development").lower()
    
    if env == "production":
        return ProductionConfig()
    else:
        return DevelopmentConfig()
