#!/usr/bin/env python3
"""
Test script for Azure OpenAI Memory Chat
Verifies all components are working correctly
"""

import sys
import warnings
warnings.filterwarnings("ignore")

def test_imports():
    """Test all required imports"""
    print("🧪 Testing imports...")
    
    try:
        import streamlit as st
        print("  ✅ Streamlit")
    except ImportError as e:
        print(f"  ❌ Streamlit: {e}")
        return False
    
    try:
        import chromadb
        print("  ✅ ChromaDB")
    except ImportError as e:
        print(f"  ❌ ChromaDB: {e}")
        return False
    
    try:
        from sentence_transformers import SentenceTransformer
        print("  ✅ Sentence Transformers")
    except ImportError as e:
        print(f"  ❌ Sentence Transformers: {e}")
        return False
    
    try:
        import bcrypt
        print("  ✅ bcrypt")
    except ImportError as e:
        print(f"  ❌ bcrypt: {e}")
        return False
    
    try:
        from openai import AzureOpenAI
        print("  ✅ OpenAI")
    except ImportError as e:
        print(f"  ❌ OpenAI: {e}")
        return False
    
    return True

def test_modules():
    """Test custom modules"""
    print("\n🔧 Testing custom modules...")
    
    try:
        from config import get_config
        config = get_config()
        print("  ✅ Config module")
        
        # Test configuration
        validation = config.validate_config()
        if validation['azure_endpoint'] and validation['deployment_name']:
            print("  ✅ Configuration valid")
        else:
            print("  ⚠️  Configuration needs Azure credentials")
    except Exception as e:
        print(f"  ❌ Config module: {e}")
        return False
    
    try:
        from auth_manager import AuthManager
        auth = AuthManager()
        print("  ✅ Auth manager")
    except Exception as e:
        print(f"  ❌ Auth manager: {e}")
        return False
    
    try:
        from memory_manager import VectorMemoryManager
        # Don't initialize to avoid creating database files
        print("  ✅ Memory manager")
    except Exception as e:
        print(f"  ❌ Memory manager: {e}")
        return False
    
    try:
        from azure_client import AzureOpenAIClient
        # Don't initialize to avoid API calls
        print("  ✅ Azure client")
    except Exception as e:
        print(f"  ❌ Azure client: {e}")
        return False
    
    return True

def test_database():
    """Test database connectivity"""
    print("\n🗄️ Testing database...")
    
    try:
        import chromadb
        from chromadb.config import Settings
        
        # Test ChromaDB initialization
        client = chromadb.Client(Settings(
            anonymized_telemetry=False,
            allow_reset=True
        ))
        
        # Create a test collection
        collection = client.create_collection("test_collection")
        
        # Clean up
        client.delete_collection("test_collection")
        
        print("  ✅ ChromaDB working")
        return True
    except Exception as e:
        print(f"  ❌ ChromaDB: {e}")
        return False

def test_azure_config():
    """Test Azure OpenAI configuration"""
    print("\n☁️ Testing Azure OpenAI configuration...")
    
    try:
        from config import get_config
        config = get_config()
        azure_config = config.get_azure_config()
        
        required_fields = ['api_key', 'azure_endpoint', 'deployment_name', 'api_version']
        
        for field in required_fields:
            if field in azure_config and azure_config[field]:
                print(f"  ✅ {field}: configured")
            else:
                print(f"  ❌ {field}: missing")
                return False
        
        # Test if API key looks valid (not default)
        if azure_config['api_key'].startswith('AVDB79'):
            print("  ✅ API key: appears valid")
        else:
            print("  ⚠️  API key: may need updating")
        
        return True
    except Exception as e:
        print(f"  ❌ Azure config: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Azure OpenAI Memory Chat - System Test")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("Modules", test_modules),
        ("Database", test_database),
        ("Azure Config", test_azure_config)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"  ❌ {test_name} failed: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The application is ready to run.")
        print("\n🚀 To start the application:")
        print("   python run_app.py")
        print("   OR")
        print("   streamlit run streamlit_app.py")
    else:
        print("⚠️  Some tests failed. Please check the configuration.")
        print("\n🔧 To fix issues:")
        print("   1. Run: python install.py")
        print("   2. Edit .env file with your Azure credentials")
        print("   3. Run tests again: python test_app.py")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
