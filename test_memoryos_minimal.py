#!/usr/bin/env python3
"""
Minimal test to see if MemoryOS works with different configurations
"""

import os
from memoryos import Memoryos

def test_with_mock_responses():
    """Test MemoryOS with minimal configuration to see where it fails"""
    print("Testing MemoryOS with minimal configuration...")
    
    # Use a simple configuration
    try:
        memo = Memoryos(
            user_id="test_user",
            openai_api_key="test_key",  # This will fail but let's see how
            data_storage_path="./test_data",
            llm_model="gpt-3.5-turbo",  # Use standard model name
            assistant_id="test_assistant",
            short_term_capacity=2,  # Small capacity to avoid processing
        )
        
        print("✅ MemoryOS initialized")
        
        # Try to add a simple memory (this should work without API calls)
        memo.add_memory(
            user_input="Hello",
            agent_response="Hi there!"
        )
        print("✅ Memory added successfully")
        
        # This will likely fail due to API call
        response = memo.get_response("How are you?")
        print(f"✅ Response: {response}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def test_azure_with_different_url_format():
    """Test with different Azure URL format"""
    print("\nTesting with different Azure URL format...")
    
    # Try with the deployment-specific URL format
    base_url = "https://a-cs--m30akd8h-westeurope.openai.azure.com/openai/deployments/gpt-35-turbo"
    api_key = "AVDB79YwEuh2OHb5VSNq5TTJjWDvJdqyBqzu3VR8Bnp8OyJjfx1eJQQJ99AKAC5RqLJXJ3w3AAABACOG3nTH"
    
    try:
        # Set environment for Azure
        os.environ["OPENAI_API_VERSION"] = "2024-02-01"
        os.environ["OPENAI_API_KEY"] = api_key
        
        memo = Memoryos(
            user_id="test_user2",
            openai_api_key=api_key,
            openai_base_url=base_url,
            data_storage_path="./test_data2",
            llm_model="gpt-35-turbo",  # This might be ignored with deployment URL
            assistant_id="test_assistant2",
            short_term_capacity=2,
        )
        
        print("✅ MemoryOS initialized with deployment URL")
        
        memo.add_memory(
            user_input="Test",
            agent_response="Test response"
        )
        print("✅ Memory added")
        
        # This is where the API call happens
        response = memo.get_response("What did I say?")
        print(f"✅ Response: {response}")
        
    except Exception as e:
        print(f"❌ Error with deployment URL: {e}")

def main():
    print("MemoryOS Configuration Testing")
    print("=" * 50)
    
    test_with_mock_responses()
    test_azure_with_different_url_format()
    
    print("\n" + "=" * 50)
    print("CONCLUSION:")
    print("If both tests fail, the issue is likely that MemoryOS")
    print("doesn't properly support Azure OpenAI configuration.")
    print("You may need to:")
    print("1. Use regular OpenAI API instead of Azure")
    print("2. Find a different memory system")
    print("3. Modify MemoryOS source code to support Azure properly")

if __name__ == "__main__":
    main()
