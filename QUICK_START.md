# Quick Start Guide - Azure OpenAI Memory Chat

## 🚀 Running the Application

### Option 1: Using the Startup Script (Recommended)
```bash
python run_app.py
```

### Option 2: Direct Streamlit Command
```bash
streamlit run streamlit_app.py --server.port 8501 --server.address 0.0.0.0
```

### Option 3: Using Configuration File
```bash
streamlit run streamlit_app.py
```

## 🌐 Accessing the Application

Once started, open your browser and go to:
- **Local Access**: http://localhost:8501
- **Network Access**: http://0.0.0.0:8501

## 👤 First Time Setup

### 1. Register a New Account
- Click on the "Register" tab
- Choose a username and password
- Click "Register"

### 2. Login
- Switch to the "Login" tab
- Enter your credentials
- Click "Login"

### 3. Start Chatting
- Type your message in the chat input
- Press Enter or click Send
- The AI will respond with context from your conversation history

## 🎯 Key Features to Try

### **Memory Testing**
1. **Introduce Yourself**: "Hi, I'm <PERSON> and I work as a software engineer"
2. **Ask Later**: "What do you remember about my job?"
3. **See the Magic**: The AI remembers your previous conversation!

### **Context Awareness**
1. **Discuss a Topic**: "I'm working on a Python project"
2. **Continue Later**: "Can you help me with the project we discussed?"
3. **Seamless Continuity**: The AI maintains context across sessions

### **User Management**
- **View Stats**: Check your conversation count in the sidebar
- **Clear History**: Use the "Clear Chat History" button
- **Logout**: Secure session termination

## 🛠️ Troubleshooting

### **Application Won't Start**
```bash
# Test the system
python test_app.py

# If tests fail, reinstall
python install.py
```

### **Can't Access the URL**
- Try http://localhost:8501 instead of 0.0.0.0
- Check if port 8501 is available
- Restart the application

### **Login Issues**
- Make sure you registered first
- Check username/password spelling
- Try registering a new account

### **AI Not Responding**
- Check your Azure OpenAI credentials in .env
- Verify your deployment name is correct
- Check the terminal for error messages

## 📊 Understanding the Interface

### **Main Chat Area**
- **User Messages**: Blue background, right-aligned
- **AI Messages**: Purple background, left-aligned
- **Typing Indicator**: Shows when AI is thinking

### **Sidebar Controls**
- **User Info**: Your username and ID
- **Clear History**: Remove all conversations
- **Logout**: End your session
- **Memory Stats**: Conversation count and metrics

### **Login/Register**
- **Tabs**: Switch between login and registration
- **Forms**: Secure input fields
- **Validation**: Real-time error checking

## 🔧 Advanced Usage

### **Environment Variables**
Edit `.env` file to customize:
```env
AZURE_OPENAI_API_KEY=your-key
AZURE_OPENAI_ENDPOINT=your-endpoint
AZURE_OPENAI_DEPLOYMENT=your-deployment
MAX_CONVERSATION_HISTORY=50
MAX_CONTEXT_RESULTS=5
```

### **Configuration**
Edit `.streamlit/config.toml` for UI customization:
```toml
[theme]
primaryColor = "#2E86AB"
backgroundColor = "#FFFFFF"
```

### **Database Management**
- **Location**: `./chroma_db/` directory
- **Backup**: Copy the entire `chroma_db` folder
- **Reset**: Delete `chroma_db` folder to start fresh

## 🚨 Important Notes

### **Data Privacy**
- All data stored locally
- No external data sharing
- Complete user isolation
- Secure password hashing

### **Performance**
- First response may be slower (model loading)
- Subsequent responses are faster
- Memory grows with conversations
- Automatic cleanup prevents bloat

### **Warnings**
- PyTorch warnings are normal and can be ignored
- They don't affect functionality
- Use `python run_app.py` for cleaner output

## 📞 Getting Help

### **Check System Status**
```bash
python test_app.py
```

### **View Logs**
- Check terminal output for errors
- Look in `logs/` directory for detailed logs

### **Common Solutions**
1. **Restart the application**
2. **Check .env configuration**
3. **Verify Azure OpenAI access**
4. **Clear browser cache**

## 🎉 Enjoy Your AI Chat!

You now have a fully functional AI chatbot with:
- ✅ Persistent memory across sessions
- ✅ Secure user authentication
- ✅ Context-aware conversations
- ✅ Scalable vector database storage
- ✅ Modern web interface

**Happy chatting!** 🤖💬
