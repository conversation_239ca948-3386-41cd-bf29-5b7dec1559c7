#!/usr/bin/env python3
"""
Authentication Manager for Azure OpenAI Memory System
Handles user registration, authentication, and session management.
"""

import bcrypt
import uuid
import json
import os
from typing import Optional, Dict
import streamlit as st

class AuthManager:
    """Manages user authentication and registration"""
    
    def __init__(self, users_file: str = "users.json"):
        """
        Initialize the authentication manager.
        
        Args:
            users_file: Path to the users database file
        """
        self.users_file = users_file
        self.users_db = self._load_users()
    
    def _load_users(self) -> Dict:
        """Load users database from file"""
        if os.path.exists(self.users_file):
            try:
                with open(self.users_file, 'r') as f:
                    return json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                return {}
        return {}
    
    def _save_users(self) -> None:
        """Save users database to file"""
        try:
            with open(self.users_file, 'w') as f:
                json.dump(self.users_db, f, indent=2)
        except Exception as e:
            st.error(f"Error saving users database: {e}")
    
    def _hash_password(self, password: str) -> str:
        """Hash a password using bcrypt"""
        salt = bcrypt.gensalt()
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        return hashed.decode('utf-8')
    
    def _verify_password(self, password: str, hashed: str) -> bool:
        """Verify a password against its hash"""
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    
    def register_user(self, username: str, password: str) -> bool:
        """
        Register a new user.
        
        Args:
            username: The username
            password: The password
            
        Returns:
            True if registration successful, False if username exists
        """
        if username in self.users_db:
            return False
        
        user_id = str(uuid.uuid4())
        hashed_password = self._hash_password(password)
        
        self.users_db[username] = {
            "user_id": user_id,
            "password_hash": hashed_password,
            "created_at": str(uuid.uuid1().time),
            "last_login": None
        }
        
        self._save_users()
        return True
    
    def authenticate_user(self, username: str, password: str) -> Optional[str]:
        """
        Authenticate a user.
        
        Args:
            username: The username
            password: The password
            
        Returns:
            User ID if authentication successful, None otherwise
        """
        if username not in self.users_db:
            return None
        
        user_data = self.users_db[username]
        if self._verify_password(password, user_data["password_hash"]):
            # Update last login
            user_data["last_login"] = str(uuid.uuid1().time)
            self._save_users()
            return user_data["user_id"]
        
        return None
    
    def get_user_info(self, username: str) -> Optional[Dict]:
        """
        Get user information.
        
        Args:
            username: The username
            
        Returns:
            User information dictionary or None if user doesn't exist
        """
        if username in self.users_db:
            user_data = self.users_db[username].copy()
            # Remove sensitive information
            user_data.pop("password_hash", None)
            return user_data
        return None
    
    def change_password(self, username: str, old_password: str, new_password: str) -> bool:
        """
        Change user password.
        
        Args:
            username: The username
            old_password: The current password
            new_password: The new password
            
        Returns:
            True if password changed successfully, False otherwise
        """
        if username not in self.users_db:
            return False
        
        user_data = self.users_db[username]
        if not self._verify_password(old_password, user_data["password_hash"]):
            return False
        
        user_data["password_hash"] = self._hash_password(new_password)
        self._save_users()
        return True
    
    def delete_user(self, username: str, password: str) -> bool:
        """
        Delete a user account.
        
        Args:
            username: The username
            password: The password for confirmation
            
        Returns:
            True if user deleted successfully, False otherwise
        """
        if username not in self.users_db:
            return False
        
        user_data = self.users_db[username]
        if not self._verify_password(password, user_data["password_hash"]):
            return False
        
        del self.users_db[username]
        self._save_users()
        return True
    
    def list_users(self) -> list:
        """
        List all registered users (admin function).
        
        Returns:
            List of usernames
        """
        return list(self.users_db.keys())
    
    def get_user_count(self) -> int:
        """
        Get total number of registered users.
        
        Returns:
            Number of registered users
        """
        return len(self.users_db)

# Session management utilities
class SessionManager:
    """Manages user sessions in Streamlit"""
    
    @staticmethod
    def is_authenticated() -> bool:
        """Check if user is authenticated"""
        return st.session_state.get('authenticated', False)
    
    @staticmethod
    def get_current_user() -> Optional[str]:
        """Get current authenticated username"""
        if SessionManager.is_authenticated():
            return st.session_state.get('username')
        return None
    
    @staticmethod
    def get_current_user_id() -> Optional[str]:
        """Get current authenticated user ID"""
        if SessionManager.is_authenticated():
            return st.session_state.get('user_id')
        return None
    
    @staticmethod
    def logout():
        """Logout current user"""
        # Clear authentication-related session state
        auth_keys = ['authenticated', 'username', 'user_id', 'messages', 'memory_manager', 'azure_client']
        for key in auth_keys:
            if key in st.session_state:
                del st.session_state[key]
    
    @staticmethod
    def require_auth(func):
        """Decorator to require authentication for a function"""
        def wrapper(*args, **kwargs):
            if not SessionManager.is_authenticated():
                st.error("Please login to access this feature.")
                st.stop()
            return func(*args, **kwargs)
        return wrapper
