#!/usr/bin/env python3
"""
Vector Memory Manager for Azure OpenAI Memory System
Handles conversation storage and retrieval using ChromaDB vector database.
"""

import chromadb
from chromadb.config import Settings
import uuid
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from sentence_transformers import SentenceTransformer
import json
import os
import streamlit as st

class VectorMemoryManager:
    """Manages conversation memory using vector database"""
    
    def __init__(self, 
                 db_path: str = "./chroma_db",
                 collection_name: str = "conversation_memory",
                 embedding_model: str = "all-MiniLM-L6-v2"):
        """
        Initialize the vector memory manager.
        
        Args:
            db_path: Path to ChromaDB database
            collection_name: Name of the collection
            embedding_model: Sentence transformer model for embeddings
        """
        self.db_path = db_path
        self.collection_name = collection_name
        
        # Initialize ChromaDB client
        self.client = chromadb.PersistentClient(
            path=db_path,
            settings=Settings(
                anonymized_telemetry=False,
                allow_reset=True
            )
        )
        
        # Initialize embedding model
        self.embedding_model = SentenceTransformer(embedding_model)
        
        # Get or create collection
        try:
            self.collection = self.client.get_collection(collection_name)
        except:
            self.collection = self.client.create_collection(
                name=collection_name,
                metadata={"description": "Conversation memory storage"}
            )
        
        # User profiles collection
        try:
            self.profiles_collection = self.client.get_collection("user_profiles")
        except:
            self.profiles_collection = self.client.create_collection(
                name="user_profiles",
                metadata={"description": "User profiles and preferences"}
            )
    
    def _generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for text"""
        return self.embedding_model.encode(text).tolist()
    
    def store_conversation(self, 
                          user_id: str, 
                          user_message: str, 
                          assistant_message: str,
                          metadata: Optional[Dict] = None) -> str:
        """
        Store a conversation turn in the vector database.
        
        Args:
            user_id: Unique user identifier
            user_message: User's message
            assistant_message: Assistant's response
            metadata: Additional metadata
            
        Returns:
            Conversation ID
        """
        conversation_id = str(uuid.uuid4())
        timestamp = datetime.now().isoformat()
        
        # Combine user and assistant messages for embedding
        combined_text = f"User: {user_message}\nAssistant: {assistant_message}"
        embedding = self._generate_embedding(combined_text)
        
        # Prepare metadata
        conversation_metadata = {
            "user_id": user_id,
            "timestamp": timestamp,
            "user_message": user_message,
            "assistant_message": assistant_message,
            "conversation_id": conversation_id
        }
        
        if metadata:
            conversation_metadata.update(metadata)
        
        # Store in vector database
        self.collection.add(
            embeddings=[embedding],
            documents=[combined_text],
            metadatas=[conversation_metadata],
            ids=[conversation_id]
        )
        
        # Update user profile
        self._update_user_profile(user_id, user_message, assistant_message)
        
        return conversation_id
    
    def get_relevant_context(self, 
                           user_id: str, 
                           query: str, 
                           max_results: int = 5) -> List[Dict]:
        """
        Retrieve relevant conversation context for a query.
        
        Args:
            user_id: User identifier
            query: Current user query
            max_results: Maximum number of results to return
            
        Returns:
            List of relevant conversation contexts
        """
        query_embedding = self._generate_embedding(query)
        
        # Search for relevant conversations for this user
        results = self.collection.query(
            query_embeddings=[query_embedding],
            n_results=max_results,
            where={"user_id": user_id},
            include=["documents", "metadatas", "distances"]
        )
        
        contexts = []
        if results['documents'] and results['documents'][0]:
            for i, doc in enumerate(results['documents'][0]):
                metadata = results['metadatas'][0][i]
                distance = results['distances'][0][i]
                
                contexts.append({
                    "conversation_id": metadata["conversation_id"],
                    "timestamp": metadata["timestamp"],
                    "user_message": metadata["user_message"],
                    "assistant_message": metadata["assistant_message"],
                    "relevance_score": 1 - distance,  # Convert distance to similarity
                    "document": doc
                })
        
        return contexts
    
    def _update_user_profile(self, user_id: str, user_message: str, assistant_message: str):
        """Update user profile based on conversation"""
        try:
            # Try to get existing profile
            existing_profiles = self.profiles_collection.get(
                where={"user_id": user_id}
            )
            
            profile_data = {
                "user_id": user_id,
                "last_updated": datetime.now().isoformat(),
                "total_conversations": 1,
                "recent_topics": [user_message[:100]]  # Store first 100 chars
            }
            
            if existing_profiles['ids']:
                # Update existing profile
                profile_id = existing_profiles['ids'][0]
                existing_metadata = existing_profiles['metadatas'][0]
                
                profile_data["total_conversations"] = existing_metadata.get("total_conversations", 0) + 1
                recent_topics = existing_metadata.get("recent_topics", [])
                recent_topics.append(user_message[:100])
                profile_data["recent_topics"] = recent_topics[-10:]  # Keep last 10 topics
                
                self.profiles_collection.update(
                    ids=[profile_id],
                    metadatas=[profile_data]
                )
            else:
                # Create new profile
                profile_id = f"profile_{user_id}"
                profile_embedding = self._generate_embedding(f"User profile for {user_id}")
                
                self.profiles_collection.add(
                    embeddings=[profile_embedding],
                    documents=[f"Profile for user {user_id}"],
                    metadatas=[profile_data],
                    ids=[profile_id]
                )
        
        except Exception as e:
            st.error(f"Error updating user profile: {e}")
    
    def get_user_profile(self, user_id: str) -> Optional[Dict]:
        """Get user profile"""
        try:
            results = self.profiles_collection.get(
                where={"user_id": user_id}
            )
            
            if results['metadatas']:
                return results['metadatas'][0]
            return None
        
        except Exception as e:
            st.error(f"Error retrieving user profile: {e}")
            return None
    
    def get_user_stats(self, user_id: str) -> Dict:
        """Get user statistics"""
        try:
            # Count conversations
            conversations = self.collection.get(
                where={"user_id": user_id}
            )
            
            # Get profile
            profile = self.get_user_profile(user_id)
            
            return {
                "total_conversations": len(conversations['ids']) if conversations['ids'] else 0,
                "memory_entries": len(conversations['ids']) if conversations['ids'] else 0,
                "profile_exists": profile is not None,
                "last_updated": profile.get("last_updated") if profile else None
            }
        
        except Exception as e:
            st.error(f"Error getting user stats: {e}")
            return {"total_conversations": 0, "memory_entries": 0}
    
    def clear_user_history(self, user_id: str) -> bool:
        """Clear all conversation history for a user"""
        try:
            # Get all conversations for user
            user_conversations = self.collection.get(
                where={"user_id": user_id}
            )
            
            if user_conversations['ids']:
                # Delete conversations
                self.collection.delete(
                    where={"user_id": user_id}
                )
            
            # Clear user profile
            user_profiles = self.profiles_collection.get(
                where={"user_id": user_id}
            )
            
            if user_profiles['ids']:
                self.profiles_collection.delete(
                    where={"user_id": user_id}
                )
            
            return True
        
        except Exception as e:
            st.error(f"Error clearing user history: {e}")
            return False
    
    def search_conversations(self, 
                           user_id: str, 
                           search_query: str, 
                           limit: int = 10) -> List[Dict]:
        """Search conversations by text query"""
        try:
            query_embedding = self._generate_embedding(search_query)
            
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=limit,
                where={"user_id": user_id},
                include=["documents", "metadatas", "distances"]
            )
            
            conversations = []
            if results['documents'] and results['documents'][0]:
                for i, doc in enumerate(results['documents'][0]):
                    metadata = results['metadatas'][0][i]
                    distance = results['distances'][0][i]
                    
                    conversations.append({
                        "conversation_id": metadata["conversation_id"],
                        "timestamp": metadata["timestamp"],
                        "user_message": metadata["user_message"],
                        "assistant_message": metadata["assistant_message"],
                        "relevance_score": 1 - distance,
                        "document": doc
                    })
            
            return conversations
        
        except Exception as e:
            st.error(f"Error searching conversations: {e}")
            return []
    
    def get_conversation_history(self, 
                               user_id: str, 
                               limit: int = 50) -> List[Dict]:
        """Get recent conversation history for a user"""
        try:
            results = self.collection.get(
                where={"user_id": user_id},
                include=["metadatas"]
            )
            
            if not results['metadatas']:
                return []
            
            # Sort by timestamp and limit
            conversations = sorted(
                results['metadatas'],
                key=lambda x: x.get('timestamp', ''),
                reverse=True
            )[:limit]
            
            return conversations
        
        except Exception as e:
            st.error(f"Error getting conversation history: {e}")
            return []
