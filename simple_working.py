#!/usr/bin/env python3

import os
from memoryos import Memoryos

USER_ID = "demo_user"
ASSISTANT_ID = "demo_assistant"
# API key for Azure OpenAI
API_KEY = "AVDB79YwEuh2OHb5VSNq5TTJjWDvJdqyBqzu3VR8Bnp8OyJjfx1eJQQJ99AKAC5RqLJXJ3w3AAABACOG3nTH"
# Azure OpenAI base URL - this is the key fix
BASE_URL = "https://a-cs--m30akd8h-westeurope.openai.azure.com"
# Azure OpenAI deployment name - using working deployment from test
DEPLOYMENT_NAME = "gpt-35-turbo"  # Using working deployment name
DATA_STORAGE_PATH = "./simple_demo_data"
LLM_MODEL = DEPLOYMENT_NAME  # Use the deployment name directly for Azure

def simple_demo():
    print("MemoryOS Simple Demo - Fixed Version")
    
    # 1. Initialize MemoryOS
    print("Initializing MemoryOS...")
    print(f"Using Azure OpenAI endpoint: {BASE_URL}")
    print(f"Using deployment name: {DEPLOYMENT_NAME}")
    print(f"API version: 2024-02-01")
    
    try:
        # IMPORTANT: Do NOT set OPENAI_API_TYPE=azure as it causes issues
        # Instead, let MemoryOS handle the Azure configuration through the base_url
        os.environ["OPENAI_API_VERSION"] = "2024-02-01"
        os.environ["OPENAI_API_KEY"] = API_KEY
        
        # The key is to pass the base_url correctly to MemoryOS
        memo = Memoryos(
            user_id=USER_ID,
            openai_api_key=API_KEY,
            openai_base_url=BASE_URL,  # This should make MemoryOS use Azure
            data_storage_path=DATA_STORAGE_PATH,
            llm_model=LLM_MODEL,
            assistant_id=ASSISTANT_ID,
            short_term_capacity=7,  
            mid_term_heat_threshold=5,  
            retrieval_queue_capacity=7,
            long_term_knowledge_capacity=100
        )
        print("MemoryOS initialized successfully!\n")
    except Exception as e:
        print(f"Error: {e}")
        return

    # 2. Add some basic memories
    print("Adding some memories...")
    
    memo.add_memory(
        user_input="Hi! I'm Tom, I work as a data scientist in San Francisco.",
        agent_response="Hello Tom! Nice to meet you. Data science is such an exciting field. What kind of data do you work with?"
    )
     
    test_query = "What do you remember about my job?"
    print(f"User: {test_query}")
    
    response = memo.get_response(
        query=test_query,
    )
    
    print(f"Assistant: {response}")

if __name__ == "__main__":
    simple_demo()
